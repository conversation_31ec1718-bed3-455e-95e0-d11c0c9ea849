import React from "react";
import { CreditCardIcon, TrendingDownIcon } from "lucide-react";
import { ExpensesByCategoryChart } from "@/components/expenses-by-category-chart";
import { EXPENSE_MAP, EXPENSE_TYPE, Expense } from "@/services/expense.service";
import { useExpense } from "@/contexts/ExpenseContext";
import {
  StatsGrid,
  BarChart,
  DataTable,
  formatCurrency,
  formatDate,
  formatExpenseAmount,
  getAmountColorClass,
  useExpenseChartData,
} from "@/shared/components";

const getExpenseTypeIcon = (expenseType: EXPENSE_TYPE): React.ReactNode => {
  switch (expenseType) {
    case EXPENSE_TYPE.INCOME:
      return <CreditCardIcon className="h-4 w-4 text-emerald-500" />;
    case EXPENSE_TYPE.EXPENSE:
      return <TrendingDownIcon className="h-4 w-4 text-rose-500" />;
    case EXPENSE_TYPE.DEBT_BOUGHT:
      return <TrendingDownIcon className="h-4 w-4 text-amber-500" />;
    case EXPENSE_TYPE.DEBT_GIVEN:
      return <CreditCardIcon className="h-4 w-4 text-amber-500" />;
    default:
      return null;
  }
};

function DashboardOverview() {
  const { expenseStats, loading } = useExpense();

  // Process chart data using shared hooks
  const monthlyChartData = useExpenseChartData(
    expenseStats?.monthlyData || [],
    "bar"
  );

  // Create stats cards data
  const statsCards = Object.keys(EXPENSE_MAP).map((expenseTypeKey) => {
    const expenseType = expenseTypeKey as EXPENSE_TYPE;
    const expenseAmount = expenseStats?.groupedExpense[expenseType] ?? 0;

    return {
      title: EXPENSE_MAP[expenseType],
      value: expenseAmount,
      icon: getExpenseTypeIcon(expenseType),
      formatter: formatCurrency,
    };
  });

  const legendItems = [
    { color: monthlyChartData.colors[0] || "#00C49F", label: "Income" },
    { color: monthlyChartData.colors[1] || "#FF8042", label: "Expenses" },
  ];

  // Create recent transactions table columns
  const transactionColumns = [
    {
      key: "type",
      header: "Type",
      render: (transaction: Expense) => (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">
            {transaction.type.replace("_", " ")}
          </span>
        </div>
      ),
    },
    {
      key: "name",
      header: "Name",
      render: (transaction: Expense) => (
        <span className="font-medium">{transaction.name || "Unnamed"}</span>
      ),
    },
    {
      key: "category",
      header: "Category",
    },
    {
      key: "eventDate",
      header: "Date",
      render: (transaction: Expense) => formatDate(transaction.eventDate),
    },
    {
      key: "amount",
      header: "Amount",
      className: "text-right",
      render: (transaction: Expense) => {
        const colorClass = getAmountColorClass(
          transaction.amount,
          transaction.type
        );
        const formattedAmount = formatExpenseAmount(
          transaction.amount,
          transaction.type
        );

        return (
          <span className={`font-medium ${colorClass}`}>{formattedAmount}</span>
        );
      },
    },
  ];

  return (
    <>
      {/* Stats Cards */}
      <StatsGrid cards={statsCards} loading={loading} />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Monthly Overview Chart */}
        <div className="col-span-4">
          <BarChart
            title="Monthly Overview"
            data={monthlyChartData.data}
            bars={monthlyChartData.bars}
            isLoading={loading}
            formatValue={formatCurrency}
            showLegend={true}
            legendItems={legendItems}
          />
        </div>

        {/* Expenses by Category Chart */}
        <div className="col-span-3">
          <ExpensesByCategoryChart
            categoryData={expenseStats?.expensesByCategory}
            isLoading={loading}
          />
        </div>
      </div>

      {/* Recent Transactions Table */}
      <DataTable
        title="Recent Transactions"
        description="Your most recent financial activities"
        data={expenseStats?.recentTransactions || []}
        columns={transactionColumns}
        isLoading={loading}
        emptyMessage="No transactions found. Add your first transaction!"
      />
    </>
  );
}

export default DashboardOverview;
