import React from "react";
import {
  CreditCardIcon,
  TrendingDownIcon,
} from "lucide-react";
import { ExpensesByCategory<PERSON>hart } from "@/components/expenses-by-category-chart";
import { formatCurrency } from "@/lib/utils";
import {
  EXPENSE_MAP,
  EXPENSE_TYPE,
  ExpenseStats,
  Expense,
} from "@/services/expense.service";
import { useExpense } from "@/contexts/ExpenseContext";
import { StatsGrid, BarChart, DataTable } from "@/shared/components";

const getExpenseTypeIcon = (expenseType: EXPENSE_TYPE): React.ReactNode => {
  switch (expenseType) {
    case EXPENSE_TYPE.INCOME:
      return <CreditCardIcon className="h-4 w-4 text-emerald-500" />;
    case EXPENSE_TYPE.EXPENSE:
      return <TrendingDownIcon className="h-4 w-4 text-rose-500" />;
    case EXPENSE_TYPE.DEBT_BOUGHT:
      return <TrendingDownIcon className="h-4 w-4 text-amber-500" />;
    case EXPENSE_TYPE.DEBT_GIVEN:
      return <CreditCardIcon className="h-4 w-4 text-amber-500" />;
    default:
      return null;
  }
};

function DashboardOverview() {
  const { expenseStats, loading } = useExpense();

  // Create stats cards data
  const statsCards = Object.keys(EXPENSE_MAP).map((expenseTypeKey) => {
    const expenseType = expenseTypeKey as EXPENSE_TYPE;
    const expenseAmount = expenseStats?.groupedExpense[expenseType] ?? 0;

    return {
      title: EXPENSE_MAP[expenseType],
      value: expenseAmount,
      icon: getExpenseTypeIcon(expenseType),
      formatter: formatCurrency,
    };
  });

  // Create bar chart data
  const barChartData = expenseStats?.monthlyData || [];
  const barChartBars = [
    {
      dataKey: "income",
      fill: "hsl(142.1, 76.2%, 36.3%)",
      name: "Income",
    },
    {
      dataKey: "expenses", 
      fill: "hsl(346.8, 77.2%, 49.8%)",
      name: "Expenses",
    },
  ];

  const legendItems = [
    { color: "hsl(142.1, 76.2%, 36.3%)", label: "Income" },
    { color: "hsl(346.8, 77.2%, 49.8%)", label: "Expenses" },
  ];

  // Create recent transactions table columns
  const transactionColumns = [
    {
      key: "type",
      header: "Type",
      render: (transaction: Expense) => (
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">
            {transaction.type.replace("_", " ")}
          </span>
        </div>
      ),
    },
    {
      key: "name",
      header: "Name",
      render: (transaction: Expense) => (
        <span className="font-medium">{transaction.name || "Unnamed"}</span>
      ),
    },
    {
      key: "category",
      header: "Category",
    },
    {
      key: "eventDate",
      header: "Date",
      render: (transaction: Expense) => 
        new Date(transaction.eventDate).toLocaleDateString(),
    },
    {
      key: "amount",
      header: "Amount",
      className: "text-right",
      render: (transaction: Expense) => {
        const isIncome = transaction.type === EXPENSE_TYPE.INCOME;
        const isExpense = transaction.type === EXPENSE_TYPE.EXPENSE;
        const colorClass = isIncome 
          ? "text-emerald-600" 
          : isExpense 
          ? "text-rose-600" 
          : "text-amber-600";
        
        const prefix = isIncome ? "+" : isExpense ? "-" : "";
        
        return (
          <span className={`font-medium ${colorClass}`}>
            {prefix}{formatCurrency(Math.abs(transaction.amount))}
          </span>
        );
      },
    },
  ];

  return (
    <>
      {/* Stats Cards */}
      <StatsGrid cards={statsCards} loading={loading} />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Monthly Overview Chart */}
        <div className="col-span-4">
          <BarChart
            title="Monthly Overview"
            data={barChartData}
            bars={barChartBars}
            isLoading={loading}
            formatValue={formatCurrency}
            showLegend={true}
            legendItems={legendItems}
          />
        </div>

        {/* Expenses by Category Chart */}
        <div className="col-span-3">
          <ExpensesByCategoryChart
            categoryData={expenseStats?.expensesByCategory}
            isLoading={loading}
          />
        </div>
      </div>

      {/* Recent Transactions Table */}
      <DataTable
        title="Recent Transactions"
        description="Your most recent financial activities"
        data={expenseStats?.recentTransactions || []}
        columns={transactionColumns}
        isLoading={loading}
        emptyMessage="No transactions found. Add your first transaction!"
      />
    </>
  );
}

export default DashboardOverview;
