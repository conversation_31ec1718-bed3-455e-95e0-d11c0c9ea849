"use client";

import { useState } from "react";
import { PlusIcon, Loader2, CalendarIcon } from "lucide-react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { AddTransactionSheet } from "@/components/add-transaction-sheet";
import { ExpenseProvider, useExpense } from "@/contexts/ExpenseContext";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import Transactions from "./Components/Transactions";
import DashboardOverview from "./Components/Overview";

export default function DashboardPage() {
  return (
    <ExpenseProvider>
      <DashboardContent />
    </ExpenseProvider>
  );
}

function DashboardContent() {
  const [isAddTransactionOpen, setIsAddTransactionOpen] = useState(false);
  const {
    expenseStats,
    loading,
    error,
    refreshData,
    dateRange,
    setDateRange,
    ignoreDate,
    setIgnoreDate,
  } = useExpense();

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-lg text-muted-foreground">
          Loading your financial data...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="text-center max-w-md p-6 border rounded-lg shadow-sm">
          <h2 className="text-2xl font-bold text-rose-500 mb-2">
            Error Loading Data
          </h2>
          <p className="text-muted-foreground mb-4">
            We couldn't load your financial data. Please try refreshing the
            page.
          </p>
          <Button onClick={() => refreshData()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between space-y-2 md:space-y-0">
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
          <div className="flex items-center space-x-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="h-9 border-dashed">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateRange.from && dateRange.to ? (
                    <>
                      {format(dateRange.from, "MMM d, yyyy")} -{" "}
                      {format(dateRange.to, "MMM d, yyyy")}
                    </>
                  ) : (
                    <span>Select date range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <Calendar
                  mode="range"
                  selected={{
                    from: dateRange.from,
                    to: dateRange.to,
                  }}
                  onSelect={(range) => {
                    if (range) {
                      setDateRange({
                        from: range.from,
                        to: range.to,
                      });
                    }
                  }}
                  numberOfMonths={1}
                  defaultMonth={dateRange.from}
                />
              </PopoverContent>
            </Popover>
            <Button
              onClick={() => setIsAddTransactionOpen(true)}
              className="hidden md:flex"
            >
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Transaction
            </Button>
          </div>
        </div>

        {/* Date range indicator */}
        <div className="bg-muted/50 rounded-md p-2 text-sm text-muted-foreground flex items-center justify-between">
          <div className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-2" />
            {ignoreDate ? (
              <span>
                <span className="font-medium">Showing all data</span> (date
                filter disabled)
              </span>
            ) : dateRange.from && dateRange.to ? (
              <span>
                Showing data from{" "}
                <span className="font-medium">
                  {format(dateRange.from, "MMMM d, yyyy")}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {format(dateRange.to, "MMMM d, yyyy")}
                </span>
              </span>
            ) : (
              <span>No date range selected</span>
            )}
            {loading && (
              <div className="ml-2 flex items-center">
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
                <span className="text-xs">Updating...</span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant={ignoreDate ? "default" : "outline"}
              size="sm"
              className="h-7 px-2 text-xs"
              onClick={() => setIgnoreDate(!ignoreDate)}
            >
              {ignoreDate ? "Enable Date Filter" : "Show All Data"}
            </Button>
            {!ignoreDate && (
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={() => {
                  // Reset to current month
                  const today = new Date();
                  const firstDayOfMonth = new Date(
                    today.getFullYear(),
                    today.getMonth(),
                    1
                  );
                  const lastDayOfMonth = new Date(
                    today.getFullYear(),
                    today.getMonth() + 1,
                    0
                  );
                  setDateRange({
                    from: firstDayOfMonth,
                    to: lastDayOfMonth,
                  });
                }}
              >
                Reset to current month
              </Button>
            )}
          </div>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="w-full md:w-auto">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <DashboardOverview expenseStats={expenseStats} loading={loading} />
          </TabsContent>

          <TabsContent value="transactions" className="space-y-4">
            <div className="h-full">
              <Transactions />
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <AddTransactionSheet
        open={isAddTransactionOpen}
        onOpenChange={setIsAddTransactionOpen}
        onSuccess={refreshData}
      />

      <div className="fixed right-4 bottom-4 md:hidden z-10">
        <Button
          onClick={() => setIsAddTransactionOpen(true)}
          size="icon"
          className="h-14 w-14 rounded-full shadow-lg"
        >
          <PlusIcon className="h-6 w-6" />
          <span className="sr-only">Add Transaction</span>
        </Button>
      </div>
    </div>
  );
}
