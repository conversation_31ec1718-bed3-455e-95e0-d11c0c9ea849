/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Tooltip,
} from "recharts";
import { BaseChart, ChartLoading, ChartEmpty } from "./BaseChart";

interface PieChartData {
  name: string;
  value: number;
  [key: string]: string | number;
}

interface PieChartProps {
  title: string;
  description?: string;
  data: PieChartData[];
  isLoading?: boolean;
  error?: string;
  height?: number;
  className?: string;
  colors?: string[];
  formatValue?: (value: number) => string;
  showLabels?: boolean;
  showLegend?: boolean;
  innerRadius?: number;
  outerRadius?: number;
}

const DEFAULT_COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

const RADIAN = Math.PI / 180;

const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  percent,
}: {
  cx: number;
  cy: number;
  midAngle: number;
  innerRadius: number;
  outerRadius: number;
  percent: number;
}) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor={x > cx ? "start" : "end"}
      dominantBaseline="central"
      fontSize={12}
      fontWeight="bold"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

const CustomTooltip = ({ 
  active, 
  payload, 
  formatValue 
}: {
  active?: boolean;
  payload?: any[];
  formatValue?: (value: number) => string;
}) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    return (
      <div className="bg-white p-2 border rounded shadow-sm">
        <p className="font-medium">{data.name}</p>
        <p className="text-sm">
          {formatValue ? formatValue(data.value) : data.value}
        </p>
      </div>
    );
  }
  return null;
};

export function PieChart({
  title,
  description,
  data,
  isLoading = false,
  error,
  height = 300,
  className = "",
  colors = DEFAULT_COLORS,
  formatValue,
  showLabels = true,
  showLegend = true,
  innerRadius = 0,
  outerRadius = 80,
}: PieChartProps) {
  const renderChart = () => {
    if (isLoading) return <ChartLoading height={height} />;
    if (error) return <ChartEmpty height={height} message={error} />;
    if (!data || data.length === 0) return <ChartEmpty height={height} />;

    return (
      <div style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <RechartsPieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={showLabels ? renderCustomizedLabel : false}
              outerRadius={outerRadius}
              innerRadius={innerRadius}
              fill="#8884d8"
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={colors[index % colors.length]}
                />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip formatValue={formatValue} />} />
            {showLegend && <Legend />}
          </RechartsPieChart>
        </ResponsiveContainer>
      </div>
    );
  };

  return (
    <BaseChart
      title={title}
      description={description}
      className={className}
      height={height}
    >
      {renderChart()}
    </BaseChart>
  );
}
